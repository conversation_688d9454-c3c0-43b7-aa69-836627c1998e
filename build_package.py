#!/usr/bin/env python3
"""
Build script for MCP Feedback Enhanced package.

This script builds the Rust binary and packages it with the Python wrapper.
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path


def run_command(cmd, cwd=None):
    """Run a command and return the result."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error running command: {' '.join(cmd)}")
        print(f"stdout: {result.stdout}")
        print(f"stderr: {result.stderr}")
        return False
    return True


def build_rust_binary():
    """Build the Rust binary."""
    print("Building Rust binary...")

    # Build the SSE server binary
    cmd = ["cargo", "build", "--release", "--bin", "cunzhi-sse"]
    if not run_command(cmd):
        print("Failed to build Rust binary")
        return False

    return True


def copy_binary_to_package():
    """Copy the built binary to the Python package."""
    print("Copying binary to package...")
    
    # Determine source and destination paths
    if platform.system() == "Windows":
        binary_name = "cunzhi-sse.exe"
    else:
        binary_name = "cunzhi-sse"

    source_path = Path("target") / "release" / binary_name
    dest_dir = Path("src") / "mcp_cunzhi_enhanced" / "bin"
    dest_path = dest_dir / binary_name
    
    # Create destination directory
    dest_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy binary
    if source_path.exists():
        shutil.copy2(source_path, dest_path)
        # Make executable on Unix systems
        if platform.system() != "Windows":
            os.chmod(dest_path, 0o755)
        print(f"Binary copied to {dest_path}")
        return True
    else:
        print(f"Binary not found at {source_path}")
        return False


def build_python_package():
    """Build the Python package."""
    print("Building Python package...")
    
    # Clean previous builds
    build_dir = Path("build")
    dist_dir = Path("dist")
    if build_dir.exists():
        shutil.rmtree(build_dir)
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    # Build package
    cmd = [sys.executable, "-m", "build"]
    if not run_command(cmd):
        print("Failed to build Python package")
        return False
    
    return True


def main():
    """Main build process."""
    print("Building MCP Cunzhi Enhanced package...")
    
    # Check if we're in the right directory
    if not Path("Cargo.toml").exists():
        print("Error: Cargo.toml not found. Please run this script from the project root.")
        sys.exit(1)
    
    # Build Rust binary
    if not build_rust_binary():
        sys.exit(1)
    
    # Copy binary to package
    if not copy_binary_to_package():
        sys.exit(1)
    
    # Build Python package
    if not build_python_package():
        sys.exit(1)
    
    print("Package built successfully!")
    print("You can now install it with: pip install dist/*.whl")
    print("Or publish to PyPI with: twine upload dist/*")


if __name__ == "__main__":
    main()
