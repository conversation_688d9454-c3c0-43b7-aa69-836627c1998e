// 模拟Web API服务器，用于测试前端功能
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 9191;
const HOST = '0.0.0.0';

// 模拟的应用状态
let mockState = {
  theme: 'dark',
  audioEnabled: true,
  config: {
    ui_config: {
      theme: 'dark',
      always_on_top: false
    },
    audio_config: {
      enabled: true,
      url: 'default'
    },
    telegram_config: {
      enabled: false,
      bot_token: '',
      chat_id: '',
      hide_frontend_popup: false
    }
  }
};

// API响应格式
function apiResponse(success, data = null, error = null) {
  return JSON.stringify({
    success,
    data,
    error
  });
}

// 处理API请求
function handleApiRequest(req, res, pathname) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  console.log(`[${new Date().toLocaleTimeString()}] ${req.method} ${pathname}`);

  // 处理不同的API端点
  switch (pathname) {
    case '/api/get-app-info':
      res.writeHead(200);
      res.end(apiResponse(true, '寸止 Web v0.3.8 (Mock Server)'));
      break;

    case '/api/get-theme':
      res.writeHead(200);
      res.end(apiResponse(true, mockState.theme));
      break;

    case '/api/set-theme':
      if (req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
          try {
            const { theme } = JSON.parse(body);
            mockState.theme = theme;
            mockState.config.ui_config.theme = theme;
            res.writeHead(200);
            res.end(apiResponse(true));
            console.log(`  → 主题已设置为: ${theme}`);
          } catch (e) {
            res.writeHead(400);
            res.end(apiResponse(false, null, '无效的JSON数据'));
          }
        });
      } else {
        res.writeHead(405);
        res.end(apiResponse(false, null, '方法不允许'));
      }
      break;

    case '/api/get-config':
      res.writeHead(200);
      res.end(apiResponse(true, mockState.config));
      break;

    case '/api/get-audio-notification-enabled':
      res.writeHead(200);
      res.end(apiResponse(true, mockState.audioEnabled));
      break;

    case '/api/set-audio-notification-enabled':
      if (req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
          try {
            if (!body.trim()) {
              res.writeHead(400);
              res.end(apiResponse(false, null, '请求体为空'));
              return;
            }

            const enabled = JSON.parse(body);
            // 支持直接传布尔值或包装在对象中
            const actualEnabled = typeof enabled === 'boolean' ? enabled : enabled.enabled;
            mockState.audioEnabled = actualEnabled;
            mockState.config.audio_config.enabled = actualEnabled;
            res.writeHead(200);
            res.end(apiResponse(true));
            console.log(`  → 音频通知已${actualEnabled ? '启用' : '禁用'}`);
          } catch (e) {
            console.log(`  → JSON解析错误: ${e.message}, 原始数据: "${body}"`);
            res.writeHead(400);
            res.end(apiResponse(false, null, `无效的JSON数据: ${e.message}`));
          }
        });
      } else {
        res.writeHead(405);
        res.end(apiResponse(false, null, '方法不允许'));
      }
      break;

    case '/api/get-telegram-config':
      res.writeHead(200);
      res.end(apiResponse(true, mockState.config.telegram_config));
      break;

    case '/api/test-audio-sound':
    case '/api/play-notification-sound':
    case '/api/stop-audio-sound':
      res.writeHead(200);
      res.end(apiResponse(true, null));
      console.log(`  → 音频操作: ${pathname.split('/').pop()}`);
      break;

    default:
      res.writeHead(404);
      res.end(apiResponse(false, null, `API端点不存在: ${pathname}`));
      break;
  }
}

// 处理静态文件
function handleStaticFile(req, res, pathname) {
  let filePath;
  
  if (pathname === '/' || pathname === '/index.html') {
    filePath = path.join(__dirname, 'dist', 'index.html');
  } else if (pathname === '/test-web.html') {
    filePath = path.join(__dirname, 'test-web.html');
  } else if (pathname.startsWith('/assets/')) {
    filePath = path.join(__dirname, 'dist', pathname);
  } else {
    // SPA回退到index.html
    filePath = path.join(__dirname, 'dist', 'index.html');
  }

  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(404);
      res.end('文件未找到');
      return;
    }

    // 设置正确的Content-Type
    const ext = path.extname(filePath);
    const contentTypes = {
      '.html': 'text/html',
      '.js': 'application/javascript',
      '.css': 'text/css',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.ico': 'image/x-icon'
    };

    res.setHeader('Content-Type', contentTypes[ext] || 'text/plain');
    res.writeHead(200);
    res.end(data);
  });
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;

  if (pathname.startsWith('/api/')) {
    handleApiRequest(req, res, pathname);
  } else {
    handleStaticFile(req, res, pathname);
  }
});

server.listen(PORT, HOST, () => {
  console.log(`🚀 寸止 Web 模拟服务器启动成功！`);
  console.log(`📍 监听地址: http://${HOST}:${PORT}`);
  console.log(`🧪 测试页面: http://localhost:${PORT}/test-web.html`);
  console.log(`🌐 主应用: http://localhost:${PORT}/`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
  console.log('');
  console.log('📊 API端点状态:');
  console.log('  ✅ /api/get-app-info');
  console.log('  ✅ /api/get-theme');
  console.log('  ✅ /api/set-theme');
  console.log('  ✅ /api/get-config');
  console.log('  ✅ /api/get-audio-notification-enabled');
  console.log('  ✅ /api/set-audio-notification-enabled');
  console.log('  ✅ /api/get-telegram-config');
  console.log('  ✅ 音频相关API');
  console.log('');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
