[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "mcp-cunzhi-enhanced"
version = "0.3.8"
description = "Enhanced MCP server for interactive feedback and memory management"
readme = "README.md"
license = "MIT"
authors = [
    { name = "cunzhi", email = "<EMAIL>" }
]
keywords = ["mcp", "model-context-protocol", "ai", "feedback", "memory"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.8"
dependencies = [
    "requests>=2.25.0",
]

[project.urls]
Homepage = "https://github.com/cunzhi/cunzhi"
Repository = "https://github.com/cunzhi/cunzhi"
Issues = "https://github.com/cunzhi/cunzhi/issues"

[project.scripts]
mcp-cunzhi-enhanced = "mcp_cunzhi_enhanced.main:main"

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_cunzhi_enhanced"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/README.md",
    "/LICENSE",
]
