"""
MCP Cunzhi Enhanced - Interactive feedback and memory management MCP server.

This package provides a Model Context Protocol (MCP) server that supports:
- Interactive feedback with predefined options
- Memory management for project context
- Remote access via SSE transport
- Telegram integration for mobile access
"""

__version__ = "0.3.8"
__author__ = "cunzhi"
__email__ = "<EMAIL>"

from .main import main

__all__ = ["main"]
