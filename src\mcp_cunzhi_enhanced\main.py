#!/usr/bin/env python3
"""
MCP Cunzhi Enhanced - Main entry point

This module provides the main entry point for the MCP Cunzhi Enhanced server.
It handles environment variable configuration and launches the appropriate Rust binary.
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path
from typing import Optional


def find_rust_binary() -> Optional[Path]:
    """
    Find the appropriate Rust binary for the current platform.
    
    Returns:
        Path to the binary if found, None otherwise.
    """
    # Get the directory where this Python package is installed
    package_dir = Path(__file__).parent
    
    # Look for binaries in common locations
    possible_paths = [
        package_dir / "bin",
        package_dir.parent / "bin", 
        package_dir.parent.parent / "bin",
        Path.cwd() / "target" / "release",
        Path.cwd() / "target" / "debug",
    ]
    
    # Determine binary name based on platform
    if platform.system() == "Windows":
        binary_names = ["cunzhi-sse.exe", "寸止-sse.exe", "mcp-sse.exe"]
    else:
        binary_names = ["cunzhi-sse", "寸止-sse", "mcp-sse"]
    
    # Search for the binary
    for base_path in possible_paths:
        if base_path.exists():
            for binary_name in binary_names:
                binary_path = base_path / binary_name
                if binary_path.exists() and binary_path.is_file():
                    return binary_path
    
    return None


def setup_environment():
    """
    Setup environment variables for the MCP server.
    """
    # Set default values if not already set
    if "MCP_WEB_HOST" not in os.environ:
        os.environ["MCP_WEB_HOST"] = "0.0.0.0"
    
    if "MCP_WEB_PORT" not in os.environ:
        os.environ["MCP_WEB_PORT"] = "9191"
    
    # Set logging configuration for MCP mode
    if "RUST_LOG" not in os.environ:
        os.environ["RUST_LOG"] = "warn"
    
    # Set MCP log file path
    if "MCP_LOG_FILE" not in os.environ:
        import tempfile
        log_dir = Path(tempfile.gettempdir())
        os.environ["MCP_LOG_FILE"] = str(log_dir / "mcp-cunzhi-enhanced.log")


def main():
    """
    Main entry point for the MCP Cunzhi Enhanced server.
    """
    try:
        # Setup environment
        setup_environment()
        
        # Find the Rust binary
        binary_path = find_rust_binary()
        if binary_path is None:
            print("Error: Could not find MCP server binary.", file=sys.stderr)
            print("Please ensure the Rust binary is built and available.", file=sys.stderr)
            sys.exit(1)
        
        # Print startup information
        host = os.environ.get("MCP_WEB_HOST", "0.0.0.0")
        port = os.environ.get("MCP_WEB_PORT", "9191")
        print(f"Starting MCP Cunzhi Enhanced server on {host}:{port}", file=sys.stderr)
        print(f"Using binary: {binary_path}", file=sys.stderr)
        print(f"Log file: {os.environ.get('MCP_LOG_FILE', 'N/A')}", file=sys.stderr)
        
        # Launch the Rust binary
        process = subprocess.Popen(
            [str(binary_path)],
            env=os.environ.copy(),
            stdout=sys.stdout,
            stderr=sys.stderr,
        )
        
        # Wait for the process to complete
        return_code = process.wait()
        sys.exit(return_code)
        
    except KeyboardInterrupt:
        print("\nShutting down MCP server...", file=sys.stderr)
        sys.exit(0)
    except Exception as e:
        print(f"Error starting MCP server: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
