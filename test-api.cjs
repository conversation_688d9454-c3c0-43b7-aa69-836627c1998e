// 简单的API测试脚本
const http = require('http');

function testAPI(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 9191,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({
            status: res.statusCode,
            data: result
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data !== null && data !== undefined) {
      const jsonData = JSON.stringify(data);
      console.log(`   发送数据: ${jsonData}`);
      req.write(jsonData);
    }
    req.end();
  });
}

async function runTests() {
  console.log('🧪 开始API测试...\n');

  try {
    // 测试1: 获取应用信息
    console.log('1️⃣ 测试获取应用信息');
    const appInfo = await testAPI('GET', '/api/get-app-info');
    console.log(`   状态: ${appInfo.status}`);
    console.log(`   响应: ${JSON.stringify(appInfo.data)}\n`);

    // 测试2: 获取主题
    console.log('2️⃣ 测试获取主题');
    const theme = await testAPI('GET', '/api/get-theme');
    console.log(`   状态: ${theme.status}`);
    console.log(`   响应: ${JSON.stringify(theme.data)}\n`);

    // 测试3: 设置主题
    console.log('3️⃣ 测试设置主题为light');
    const setTheme = await testAPI('POST', '/api/set-theme', { theme: 'light' });
    console.log(`   状态: ${setTheme.status}`);
    console.log(`   响应: ${JSON.stringify(setTheme.data)}\n`);

    // 测试4: 再次获取主题确认更改
    console.log('4️⃣ 确认主题更改');
    const newTheme = await testAPI('GET', '/api/get-theme');
    console.log(`   状态: ${newTheme.status}`);
    console.log(`   响应: ${JSON.stringify(newTheme.data)}\n`);

    // 测试5: 获取音频状态
    console.log('5️⃣ 测试获取音频通知状态');
    const audioStatus = await testAPI('GET', '/api/get-audio-notification-enabled');
    console.log(`   状态: ${audioStatus.status}`);
    console.log(`   响应: ${JSON.stringify(audioStatus.data)}\n`);

    // 测试6: 切换音频状态
    console.log('6️⃣ 测试切换音频通知状态');
    const toggleAudio = await testAPI('POST', '/api/set-audio-notification-enabled', false);
    console.log(`   状态: ${toggleAudio.status}`);
    console.log(`   响应: ${JSON.stringify(toggleAudio.data)}\n`);

    // 测试7: 获取配置
    console.log('7️⃣ 测试获取完整配置');
    const config = await testAPI('GET', '/api/get-config');
    console.log(`   状态: ${config.status}`);
    console.log(`   响应: ${JSON.stringify(config.data, null, 2)}\n`);

    console.log('✅ 所有API测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

runTests();
